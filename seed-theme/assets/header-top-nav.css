/* 
 * Header Top Navigation Styles
 * 
 * Стили для верхней панели навигации в шапке сайта
 * Включает основные стили для элементов меню и адаптивность
 */

/* Top Bar Menu Items Styles */
#nav-top > ul > li > a {
  background-color: rgba(0, 0, 0, 0) !important;
  box-sizing: border-box;
  color: rgb(111, 111, 111) !important;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  font-family: Lato, sans-serif;
  font-size: 15px;
  height: auto;
  line-height: 21px;
  list-style: none;
  outline: none;
  text-align: left;
  text-decoration: none !important;
  width: auto;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  transition: color 0.2s ease;
}

#nav-top > ul > li > a svg,
#nav-top > ul > li > a i {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

#nav-top > ul > li > a:hover {
  color: rgb(80, 80, 80) !important;
}

/* Mobile responsive for top bar items */
@media only screen and (max-width: 760px) {
  #nav-top > ul > li.mobile-hide {
    display: none !important;
  }

  #nav-top > ul > li > a {
    font-size: 13px;
    gap: 4px;
  }

  #nav-top > ul > li > a svg,
  #nav-top > ul > li > a i {
    width: 14px;
    height: 14px;
  }
}
